import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import dts from 'vite-plugin-dts'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const isLib = mode === 'lib'

  return {
    base: '/modules/web-order-ui-demo/universal/',
    plugins: [vue(), ...(isLib ? [dts({ include: ['src/**/*'] })] : [])],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src')
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/styles/variables" as *;`
        }
      }
    },
    ...(isLib
      ? {
          build: {
            lib: {
              entry: resolve(__dirname, 'src/index.ts'),
              name: 'OdiWebBusiness',
              fileName: format => `index.${format === 'es' ? 'es.js' : 'js'}`
            },
            rollupOptions: {
              external: ['vue'],
              output: {
                globals: {
                  vue: 'Vue'
                }
              }
            }
          }
        }
      : {
          server: {
            port: 8080,
            strictPort: true,
            open: true,
            // 允许的主机列表
            allowedHosts: [
              'res-local.app.ikea.cn',
              'localhost',
              '127.0.0.1',
              '.app.ikea.cn', // 允许所有 app.ikea.cn 的子域名
              '.ingka-dt.cn'
            ]
          }
        }),
    test: {
      globals: true,
      environment: 'jsdom'
    }
  }
})
