<template>
  <div class="home">
    <div class="hero-section">
      <h1>@odi/web-odi-business</h1>
      <p class="hero-description">ODI团队出品的一套业务组件库</p>
      <div class="hero-actions">
        <router-link to="/components" class="btn btn-primary">
          查看组件
        </router-link>
        <a 
          href="https://res-qa.app.ikea.cn/modules/storybook/ikeacn-ui/?path=/story/ikeacn-local-activitybanner--default" 
          target="_blank" 
          class="btn btn-secondary"
        >
          ikeacn UI文档
        </a>
      </div>
    </div>

    <div class="features-section">
      <h2>特性</h2>
      <div class="features-grid">
        <div class="feature-card">
          <h3>🚀 Vue 3 + TypeScript</h3>
          <p>基于最新的Vue 3 Composition API和TypeScript开发，提供完整的类型支持</p>
        </div>
        <div class="feature-card">
          <h3>📦 组件化设计</h3>
          <p>高度可复用的业务组件，遵循统一的设计规范和交互标准</p>
        </div>
        <div class="feature-card">
          <h3>🎨 主题定制</h3>
          <p>支持主题定制，可以轻松适配不同的设计需求</p>
        </div>
        <div class="feature-card">
          <h3>📱 响应式设计</h3>
          <p>完美适配各种屏幕尺寸，提供一致的用户体验</p>
        </div>
        <div class="feature-card">
          <h3>🧪 完整测试</h3>
          <p>包含单元测试和E2E测试，确保组件的稳定性和可靠性</p>
        </div>
        <div class="feature-card">
          <h3>📚 详细文档</h3>
          <p>提供完整的API文档和使用示例，快速上手</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面逻辑
</script>

<style scoped>
.home {
  padding: 0;
}

.hero-section {
  background: linear-gradient(135deg, #0058a3, #0078d4);
  color: white;
  padding: 4rem 2rem;
  text-align: center;
}

.hero-section h1 {
  font-size: 3rem;
  margin: 0 0 1rem 0;
  font-weight: 700;
}

.hero-description {
  font-size: 1.25rem;
  margin: 0 0 2rem 0;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-primary {
  background: white;
  color: #0058a3;
}

.btn-primary:hover {
  background: #f0f0f0;
  transform: translateY(-1px);
}

.btn-secondary {
  background: transparent;
  color: white;
  border: 2px solid white;
}

.btn-secondary:hover {
  background: white;
  color: #0058a3;
  transform: translateY(-1px);
}

.features-section {
  padding: 4rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.features-section h2 {
  text-align: center;
  font-size: 2.5rem;
  margin: 0 0 3rem 0;
  color: #0058a3;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.feature-card h3 {
  font-size: 1.25rem;
  margin: 0 0 1rem 0;
  color: #424242;
}

.feature-card p {
  color: #757575;
  line-height: 1.6;
  margin: 0;
}

@media (max-width: 768px) {
  .hero-section h1 {
    font-size: 2rem;
  }
  
  .hero-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
}
</style>
