<template>
  <div class="components-page">
    <div class="page-header">
      <h1>组件展示</h1>
      <p>这里展示了@odi/web-odi-business组件库中的各种业务组件</p>
    </div>

    <div class="components-content">
      <section class="demo-section">
        <h2>Button 按钮组件</h2>
        <div class="component-demo">
          <div class="demo-group">
            <h4>基础用法</h4>
            <div class="demo-buttons">
              <Button type="primary">Primary</Button>
              <Button type="secondary">Secondary</Button>
              <Button type="success">Success</Button>
              <Button type="warning">Warning</Button>
              <Button type="danger">Danger</Button>
              <Button type="default">Default</Button>
            </div>
          </div>

          <div class="demo-group">
            <h4>不同尺寸</h4>
            <div class="demo-buttons">
              <Button size="small">Small</Button>
              <Button size="medium">Medium</Button>
              <Button size="large">Large</Button>
            </div>
          </div>

          <div class="demo-group">
            <h4>状态</h4>
            <div class="demo-buttons">
              <Button>Normal</Button>
              <Button disabled>Disabled</Button>
              <Button loading>Loading</Button>
            </div>
          </div>
        </div>
      </section>

      <!-- 可以在这里添加更多组件的演示 -->
      <section class="demo-section">
        <h2>更多组件</h2>
        <div class="component-demo">
          <p>更多业务组件正在开发中...</p>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Button } from '../components'
</script>

<style scoped>
.components-page {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 3rem;
}

.page-header h1 {
  font-size: 2.5rem;
  color: #0058a3;
  margin: 0 0 1rem 0;
}

.page-header p {
  font-size: 1.1rem;
  color: #757575;
  margin: 0;
}

.components-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.demo-section h2 {
  color: #0058a3;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.component-demo {
  background: #f9f9f9;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1.5rem;
}

.demo-group {
  margin: 1.5rem 0;
}

.demo-group:first-child {
  margin-top: 0;
}

.demo-group:last-child {
  margin-bottom: 0;
}

.demo-group h4 {
  margin: 0 0 1rem 0;
  color: #616161;
  font-size: 1rem;
  font-weight: 500;
}

.demo-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  align-items: center;
}

@media (max-width: 768px) {
  .components-page {
    padding: 1rem;
  }
  
  .page-header h1 {
    font-size: 2rem;
  }
  
  .demo-buttons {
    flex-direction: column;
    align-items: stretch;
  }
}
</style>
