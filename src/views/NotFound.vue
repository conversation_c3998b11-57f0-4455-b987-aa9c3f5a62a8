<template>
  <div class="not-found">
    <div class="not-found-content">
      <div class="error-code">404</div>
      <h1>页面未找到</h1>
      <p>抱歉，您访问的页面不存在或已被移除。</p>
      <div class="actions">
        <router-link to="/" class="btn btn-primary"> 返回首页 </router-link>
        <router-link to="/components" class="btn btn-secondary"> 查看组件 </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 404页面逻辑
</script>

<style scoped>
.not-found {
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.not-found-content {
  text-align: center;
  max-width: 500px;
}

.error-code {
  font-size: 8rem;
  font-weight: 700;
  color: #0058a3;
  line-height: 1;
  margin-bottom: 1rem;
  opacity: 0.8;
}

.not-found-content h1 {
  font-size: 2rem;
  color: #424242;
  margin: 0 0 1rem;
}

.not-found-content p {
  font-size: 1.1rem;
  color: #757575;
  margin: 0 0 2rem;
  line-height: 1.6;
}

.actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #0058a3;
  color: white;
}

.btn-primary:hover {
  background: #004080;
  transform: translateY(-1px);
}

.btn-secondary {
  background: transparent;
  color: #0058a3;
  border: 2px solid #0058a3;
}

.btn-secondary:hover {
  background: #0058a3;
  color: white;
  transform: translateY(-1px);
}

@media (width <= 768px) {
  .error-code {
    font-size: 6rem;
  }

  .not-found-content h1 {
    font-size: 1.5rem;
  }

  .actions {
    flex-direction: column;
    align-items: center;
  }
}
</style>
