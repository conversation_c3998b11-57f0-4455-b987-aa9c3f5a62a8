import { describe, it, expect, beforeEach } from 'vitest'
import { createRouter, createMemoryHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// 导入路由配置
const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Home',
    component: { template: '<div>Home</div>' },
    meta: {
      title: '首页'
    }
  },
  {
    path: '/components',
    name: 'Components',
    component: { template: '<div>Components</div>' },
    meta: {
      title: '组件展示'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: { template: '<div>NotFound</div>' },
    meta: {
      title: '页面未找到'
    }
  }
]

describe('Router', () => {
  let router: ReturnType<typeof createRouter>

  beforeEach(() => {
    router = createRouter({
      history: createMemoryHistory(),
      routes
    })
  })

  it('should have correct routes configured', () => {
    const routeRecords = router.getRoutes()

    // 检查路由数量
    expect(routeRecords).toHaveLength(3)

    // 检查首页路由
    const homeRoute = routeRecords.find(route => route.name === 'Home')
    expect(homeRoute).toBeDefined()
    expect(homeRoute?.path).toBe('/')
    expect(homeRoute?.meta?.title).toBe('首页')

    // 检查组件页路由
    const componentsRoute = routeRecords.find(route => route.name === 'Components')
    expect(componentsRoute).toBeDefined()
    expect(componentsRoute?.path).toBe('/components')
    expect(componentsRoute?.meta?.title).toBe('组件展示')

    // 检查404路由
    const notFoundRoute = routeRecords.find(route => route.name === 'NotFound')
    expect(notFoundRoute).toBeDefined()
    expect(notFoundRoute?.path).toBe('/:pathMatch(.*)*')
    expect(notFoundRoute?.meta?.title).toBe('页面未找到')
  })

  it('should navigate to home route', async () => {
    await router.push('/')

    expect(router.currentRoute.value.name).toBe('Home')
    expect(router.currentRoute.value.path).toBe('/')
  })

  it('should navigate to components route', async () => {
    await router.push('/components')

    expect(router.currentRoute.value.name).toBe('Components')
    expect(router.currentRoute.value.path).toBe('/components')
  })

  it('should handle unknown routes with 404', async () => {
    await router.push('/unknown-route')

    expect(router.currentRoute.value.name).toBe('NotFound')
    expect(router.currentRoute.value.params.pathMatch).toEqual(['unknown-route'])
  })
})
