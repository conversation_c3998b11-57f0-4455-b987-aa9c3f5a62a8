<template>
  <div class="app">
    <header class="app-header">
      <div class="header-content">
        <router-link to="/" class="logo"> @odi/web-odi-business </router-link>
        <nav class="nav">
          <router-link to="/" class="nav-link">首页</router-link>
          <router-link to="/components" class="nav-link">组件</router-link>
        </nav>
      </div>
    </header>

    <main class="app-main">
      <router-view />
    </main>

    <footer class="app-footer">
      <p>&copy; 2024 ODI Team. All rights reserved.</p>
    </footer>
  </div>
</template>

<script setup lang="ts">
// App组件逻辑
</script>

<style scoped>
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.app-header {
  background: linear-gradient(135deg, #0058a3, #0078d4);
  color: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 8px rgb(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  text-decoration: none;
  transition: opacity 0.2s ease;
}

.logo:hover {
  opacity: 0.8;
}

.nav {
  display: flex;
  gap: 2rem;
}

.nav-link {
  color: white;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.nav-link:hover {
  background: rgb(255, 255, 255, 0.1);
}

.nav-link.router-link-active {
  background: rgb(255, 255, 255, 0.2);
}

.app-main {
  flex: 1;
  min-height: 0;
}

.app-footer {
  background: #f5f5f5;
  padding: 1rem;
  text-align: center;
  color: #757575;
  border-top: 1px solid #e0e0e0;
}

.app-footer p {
  margin: 0;
}

@media (width <= 768px) {
  .app-header {
    padding: 1rem;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
  }

  .nav {
    gap: 1rem;
  }
}
</style>
