// ODI Business Components 类型定义

// 路由相关类型
export interface RouteMetaData {
  title?: string
  requiresAuth?: boolean
  roles?: string[]
  keepAlive?: boolean
}

// 基础类型
export type Size = 'small' | 'medium' | 'large'
export type Variant = 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info'
export type Theme = 'light' | 'dark'

// 组件通用属性
export interface BaseComponentProps {
  /**
   * 组件大小
   */
  size?: Size
  /**
   * 是否禁用
   */
  disabled?: boolean
  /**
   * 自定义类名
   */
  className?: string
  /**
   * 自定义样式
   */
  style?: Record<string, any>
}

// 按钮相关类型
export interface ButtonProps extends BaseComponentProps {
  /**
   * 按钮类型
   */
  variant?: Variant
  /**
   * 是否为块级按钮
   */
  block?: boolean
  /**
   * 是否显示加载状态
   */
  loading?: boolean
  /**
   * 按钮形状
   */
  shape?: 'default' | 'round' | 'circle'
  /**
   * HTML按钮类型
   */
  htmlType?: 'button' | 'submit' | 'reset'
  /**
   * 点击事件
   */
  onClick?: (event: MouseEvent) => void
}

// 表单相关类型
export interface FormItemProps extends BaseComponentProps {
  /**
   * 字段名
   */
  name?: string
  /**
   * 标签文本
   */
  label?: string
  /**
   * 是否必填
   */
  required?: boolean
  /**
   * 错误信息
   */
  error?: string
  /**
   * 帮助文本
   */
  help?: string
}

// 输入框相关类型
export interface InputProps extends BaseComponentProps {
  /**
   * 输入框类型
   */
  type?: 'text' | 'password' | 'email' | 'number' | 'tel' | 'url'
  /**
   * 占位符
   */
  placeholder?: string
  /**
   * 输入框值
   */
  value?: string | number
  /**
   * 默认值
   */
  defaultValue?: string | number
  /**
   * 是否只读
   */
  readonly?: boolean
  /**
   * 最大长度
   */
  maxLength?: number
  /**
   * 输入事件
   */
  onInput?: (value: string | number) => void
  /**
   * 变化事件
   */
  onChange?: (value: string | number) => void
  /**
   * 焦点事件
   */
  onFocus?: (event: FocusEvent) => void
  /**
   * 失焦事件
   */
  onBlur?: (event: FocusEvent) => void
}
