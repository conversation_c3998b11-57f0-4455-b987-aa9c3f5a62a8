name: CI Simple (GHES Compatible)

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: latest

      - name: Setup Node.js 18
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Run type checking
        run: pnpm run typecheck

      - name: Run linting
        run: pnpm run lint:ci

      - name: Run unit tests
        run: pnpm run test

      - name: Run tests with coverage
        run: pnpm run test:coverage

      - name: Build library
        run: pnpm run build:lib

      - name: Display build results
        run: |
          echo "✅ Build completed successfully!"
          echo "📦 Library files:"
          ls -la dist/
          echo ""
          echo "📊 Coverage summary:"
          if [ -f coverage/coverage-summary.json ]; then
            cat coverage/coverage-summary.json
          else
            echo "Coverage summary not found"
          fi
