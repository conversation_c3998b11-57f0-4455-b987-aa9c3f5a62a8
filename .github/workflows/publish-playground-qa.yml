# This workflow will run tests using node and then publish a package to GitHub Packages when a release is created
# For more information see: https://help.github.com/actions/language-and-framework-guides/publishing-nodejs-packages

name: Publish Playground to QA

on:
  workflow_dispatch:

jobs:
  build:
    runs-on: [ self-hosted, ali ]
    steps:
      - name: Login to Artifactory
        env:
          DOCKER_USERNAME: ${{ secrets.RT_CDH_USERNAME }}
          DOCKER_USERPWD: ${{ secrets.RT_CDH_TOKEN }}
        run: |
          docker login artifactory.cloud.ingka-system.cn -u $DOCKER_USERNAME -p $DOCKER_USERPWD
      - uses: ikea-github-actions/checkout@v2
      - uses: actions/setup-node@v3
        with:
          node-version: 18
          check-latest: true
      - name: Install pnpm
        uses: pnpm/action-setup@v2.0.1
        with:
          version: 10
     
      - name: setup npm registry
        run: |
          npm config set registry https://artifactory.cloud.ingka-system.cn/artifactory/api/npm/cn-digital-hub-npm-virtual/
          npm config set //artifactory.cloud.ingka-system.cn/artifactory/api/npm/cn-digital-hub-npm-virtual/:_password=${{ secrets.RT_CDH_NPM_PASSWORD }}
          npm config set //artifactory.cloud.ingka-system.cn/artifactory/api/npm/cn-digital-hub-npm-virtual/:username=${{ secrets.RT_CDH_USERNAME }}

      - name: Install root dependencies and build main project
        run: |
          pnpm install --no-frozen-lockfile
          pnpm run build:lib

          # 复制 package.json 到 dist 目录，以便作为独立包使用
          cp package.json dist/

          # 修正 dist/package.json 中的路径引用
          cd dist
          # 使用 perl 替代 sed 以确保跨平台兼容性
          perl -i -pe 's|"./dist/|"./|g' package.json
          perl -i -pe 's|"dist"|"."|g' package.json

          # 如果 index.d.ts 不存在，从 package.json 中移除 types 字段
          if [ ! -f "index.d.ts" ]; then
            echo "index.d.ts not found, removing types field from package.json"
            perl -i -pe 's|"types": "./index.d.ts",||g' package.json
            perl -i -pe 's|"types": "./index.d.ts"||g' package.json
            # 同时从 exports 中移除 types 字段
            perl -i -pe 's|"types": "./index.d.ts"||g' package.json
            perl -i -pe 's|,\s*}|}|g' package.json
          fi
          cd ..

          # 验证构建结果
          echo "=== 检查主项目构建结果 ==="
          ls -la dist/
          echo "=== 检查 dist 中的 package.json ==="
          cat dist/package.json | grep -A 10 '"exports"'
          echo "=== 检查 dist/index.js 是否存在 ==="
          ls -la dist/index.js || echo "index.js not found"
          ls -la dist/index.d.ts || echo "index.d.ts not found (will be handled)"
      - name: Setup playground as independent project
        run: |
          # 进入 playground 目录
          cd playground

          # 修改 package.json，将 workspace 依赖改为 file 依赖
          cat > package.json << 'EOF'
          {
            "name": "playground",
            "private": true,
            "version": "0.0.0",
            "scripts": {
              "dev": "vite",
              "build": "vue-tsc && vite build",
              "build-qa": "NODE_ENV=development vite build --mode qa",
              "deploy-qa": "pnpm run build-qa && pnpm exec ikea deploy --env qa",
              "preview": "vite preview"
            },
            "dependencies": {
              "@odi/web-odi-use": "file:../dist",
              "bootstrap": "^5.3.7",
              "bootstrap-icons": "^1.13.1",
              "bootstrap-vue-next": "^0.30.4",
              "vue": "^3.4.0",
              "vue-router": "^4.5.1"
            },
            "devDependencies": {
              "@originjs/vite-plugin-commonjs": "^1.0.3",
              "@vitejs/plugin-vue": "^5.0.0",
              "ikea-jssdk-cli": "^2.0.3",
              "typescript": "^5.0.0",
              "vite": "^5.0.0",
              "vue-tsc": "^1.8.0"
            }
          }
          EOF

          # 创建独立的 tsconfig.json
          cat > tsconfig.json << 'EOF'
          {
            "compilerOptions": {
              "target": "ES2020",
              "useDefineForClassFields": true,
              "lib": ["ES2020", "DOM", "DOM.Iterable"],
              "module": "ESNext",
              "skipLibCheck": true,
              "moduleResolution": "bundler",
              "allowImportingTsExtensions": true,
              "resolveJsonModule": true,
              "isolatedModules": true,
              "noEmit": true,
              "jsx": "preserve",
              "strict": true,
              "noUnusedLocals": true,
              "noUnusedParameters": true,
              "noFallthroughCasesInSwitch": true
            },
            "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"],
            "references": [{ "path": "./tsconfig.node.json" }]
          }
          EOF

          # 创建 tsconfig.node.json
          cat > tsconfig.node.json << 'EOF'
          {
            "compilerOptions": {
              "composite": true,
              "skipLibCheck": true,
              "module": "ESNext",
              "moduleResolution": "bundler",
              "allowSyntheticDefaultImports": true
            },
            "include": ["vite.config.ts"]
          }
          EOF

          # 修改 vite.config.ts，使用正确的配置
          cat > vite.config.ts << 'EOF'
          import { fileURLToPath, URL } from 'node:url'

          import { defineConfig } from 'vite'
          import vue from '@vitejs/plugin-vue'
          import { viteCommonjs } from '@originjs/vite-plugin-commonjs'

          export default defineConfig({
            base: '/modules/web-odi-use/universal/',
            plugins: [
              vue(),
              viteCommonjs({
                include: ['**/ikea.config.js'],
                skipPreBuild: true
              }),
            ],
            resolve: {
              alias: {
                '@': fileURLToPath(new URL('./src', import.meta.url))
              },
            },
            assetsInclude: ['**/*.woff2', '**/*.woff', '**/*.ttf'],
            server: {
              host: '0.0.0.0',
              port: 8080,
              allowedHosts: [
                'localhost',
                '127.0.0.1',
                'res-local.app.ikea.cn',
                '.app.ikea.cn',
                '.ingka-dt.cn'
              ]
            },
            build: {
              rollupOptions: {
                output: {
                  assetFileNames: (assetInfo) => {
                    if (assetInfo.names && assetInfo.names[0] && /\.(woff2?|ttf|otf)$/.test(assetInfo.names[0])) {
                      return 'fonts/[name]-[hash][extname]'
                    }
                    return 'assets/[name]-[hash][extname]'
                  }
                }
              }
            }
          })
          EOF

          echo "Created independent configurations"
          ls -la *.json *.ts
      - name: Install playground dependencies
        run: |
          cd playground
          echo "=== 检查 playground package.json ==="
          cat package.json
          echo "=== 检查上级目录的主项目文件 ==="
          ls -la ../dist/ || echo "dist 目录不存在"
          ls -la ../package.json || echo "package.json 不存在"
          echo "=== 安装依赖 ==="
          pnpm install --no-frozen-lockfile
      - name: Build playground for QA
        run: cd playground && pnpm run build-qa
      - name: Deploy playground to QA
        env:
          OSS_BUCKET: ikea-app-qa
          OSS_ACCESS_KEY_ID: ${{ secrets.CNAPP_OSS_ACCESS_KEY_ID_TEST }}
          OSS_ACCESS_KEY_SECRET: ${{ secrets.CNAPP_OSS_ACCESS_KEY_SECRET_TEST }}
        run: |
          cd playground
          echo "=== 当前工作目录 ==="
          pwd
          echo "=== 检查 ikea.config.js 文件 ==="
          ls -la ikea.config.js
          cat ikea.config.js
          echo "=== 检查环境变量 ==="
          echo "OSS_BUCKET: $OSS_BUCKET"
          echo "OSS_ACCESS_KEY_ID: ${OSS_ACCESS_KEY_ID:0:8}..."
          echo "=== 执行 ikea deploy ==="
          pnpm exec ikea deploy --env qa
