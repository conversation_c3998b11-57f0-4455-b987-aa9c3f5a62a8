name: CI/CD Pipeline

on:
  push:
    branches:
      - qa
      - master
  pull_request:
    branches:
      - qa
      - master
  # 支持手工触发发布
  workflow_dispatch:
    inputs:
      branch:
        description: '发布分支'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - qa
          - master
      version_type:
        description: '版本类型'
        required: false
        type: choice
        default: 'auto'
        options:
          - auto # 根据分支自动选择
          - alpha # 预发布版本 (dev)
          - beta # 测试版本 (qa)
          - release # 正式版本 (master)
          - patch # 补丁版本
          - minor # 次版本
          - major # 主版本
      skip_tests:
        description: '跳过测试（快速发布）'
        required: false
        default: false
        type: boolean
      dry_run:
        description: '干运行模式（不实际发布）'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '18'
  PNPM_VERSION: 'latest'

jobs:
  # 设置共享环境和缓存
  setup:
    name: Setup Environment
    runs-on: [self-hosted, ali]
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Setup Node.js and pnpm
        uses: ./.github/actions/setup-node-pnpm
        with:
          node-version: ${{ env.NODE_VERSION }}
          pnpm-version: ${{ env.PNPM_VERSION }}
          npm-password: ${{ secrets.RT_CDH_NPM_PASSWORD }}
          npm-username: ${{ secrets.RT_CDH_USERNAME }}

  # 代码质量检查和构建
  build-and-test:
    name: Build and Test
    runs-on: [self-hosted, ali]
    needs: setup
    # 跳过测试：手工触发且skip_tests=true时跳过
    if: github.event_name != 'workflow_dispatch' || github.event.inputs.skip_tests != 'true'
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Setup Node.js and pnpm
        uses: ./.github/actions/setup-node-pnpm
        with:
          node-version: ${{ env.NODE_VERSION }}
          pnpm-version: ${{ env.PNPM_VERSION }}
          install-deps: 'false' # 使用缓存，不重新安装
          npm-password: ${{ secrets.RT_CDH_NPM_PASSWORD }}
          npm-username: ${{ secrets.RT_CDH_USERNAME }}

      - name: Type check
        run: pnpm typecheck

      - name: Lint check (CI mode)
        run: |
          echo "🔍 运行 ESLint 检查 (CI 模式)..."
          echo "Node version: $(node --version)"
          echo "NPM version: $(npm --version)"
          echo "PNPM version: $(pnpm --version)"
          echo "Current directory: $(pwd)"
          echo "Environment variables:"
          env | grep -E "(CI|NODE_ENV|ESLINT)" || true
          echo "Running ESLint with CI config..."
          pnpm lint:ci || (echo "❌ ESLint failed with exit code $?" && exit 1)

      - name: Build library
        run: ./scripts/build-lib.sh

      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-artifacts
          path: |
            dist/
          retention-days: 7

  # 发布到不同环境
  publish:
    name: Publish Package
    runs-on: [self-hosted, ali]
    needs: [setup, build-and-test]
    # 自动发布：push到qa/master分支
    # 手工发布：workflow_dispatch触发（包括dev分支）
    # 注意：即使build-and-test被跳过，也允许发布继续
    if: |
      always() &&
      (needs.setup.result == 'success') &&
      (needs.build-and-test.result == 'success' || needs.build-and-test.result == 'skipped') &&
      (
        (github.event_name == 'push' && (github.ref == 'refs/heads/qa' || github.ref == 'refs/heads/master')) ||
        (github.event_name == 'workflow_dispatch')
      )
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Node.js and pnpm
        uses: ./.github/actions/setup-node-pnpm
        with:
          node-version: ${{ env.NODE_VERSION }}
          pnpm-version: ${{ env.PNPM_VERSION }}
          install-deps: 'false' # 使用缓存，不重新安装
          npm-password: ${{ secrets.RT_CDH_NPM_PASSWORD }}
          npm-username: ${{ secrets.RT_CDH_USERNAME }}

      - name: Download build artifacts
        if: needs.build-and-test.result == 'success'
        uses: actions/download-artifact@v3
        with:
          name: build-artifacts
          path: .

      - name: Verify downloaded artifacts
        if: needs.build-and-test.result == 'success'
        run: |
          echo "📋 验证下载的构建产物..."
          ls -la
          if [ -d "dist" ]; then
            echo "✅ dist 目录存在"
            ls -la dist/
          else
            echo "❌ dist 目录不存在"
          fi

      - name: Build library (if tests were skipped or artifacts missing)
        if: needs.build-and-test.result == 'skipped' || !hashFiles('dist/**')
        run: |
          echo "🔨 重新构建组件库..."
          pnpm install
          ./scripts/build-lib.sh

      - name: Configure Git
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"

      - name: Determine version type and bump version
        id: version
        run: |
          # 确定分支名称
          if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
            BRANCH_NAME="${{ github.event.inputs.branch }}"
            # 切换到指定分支
            git checkout $BRANCH_NAME
            git pull origin $BRANCH_NAME
          else
            BRANCH_NAME=${GITHUB_REF#refs/heads/}
          fi
          echo "branch=$BRANCH_NAME" >> $GITHUB_OUTPUT

          # 确定版本类型
          if [ "${{ github.event_name }}" == "workflow_dispatch" ] && [ "${{ github.event.inputs.version_type }}" != "auto" ]; then
            # 手工指定版本类型
            VERSION_TYPE="${{ github.event.inputs.version_type }}"
          else
            # 根据分支自动确定版本类型
            case $BRANCH_NAME in
              "dev")
                VERSION_TYPE="alpha"
                ;;
              "qa")
                VERSION_TYPE="beta"
                ;;
              "master")
                VERSION_TYPE="release"
                ;;
              *)
                echo "❌ 不支持的分支: $BRANCH_NAME"
                exit 1
                ;;
            esac
          fi

          echo "version_type=$VERSION_TYPE" >> $GITHUB_OUTPUT

          # 确定npm标签
          case $VERSION_TYPE in
            "alpha")
              echo "npm_tag=alpha" >> $GITHUB_OUTPUT
              ;;
            "beta")
              echo "npm_tag=beta" >> $GITHUB_OUTPUT
              ;;
            "release"|"patch"|"minor"|"major")
              echo "npm_tag=latest" >> $GITHUB_OUTPUT
              ;;
          esac

          echo "🎯 分支: $BRANCH_NAME"
          echo "🏷️ 版本类型: $VERSION_TYPE"

      - name: Dry run - Show what would be published
        if: github.event.inputs.dry_run == 'true'
        run: |
          echo "=== 🧪 DRY RUN MODE ==="
          echo "📦 分支: ${{ steps.version.outputs.branch }}"
          echo "🏷️ 版本类型: ${{ steps.version.outputs.version_type }}"
          echo "🎯 NPM标签: ${{ steps.version.outputs.npm_tag }}"
          echo ""
          echo "📋 将要发布的包内容:"
          npm pack --dry-run
          echo ""
          echo "⚠️ 这是干运行模式，不会实际发布包"

      - name: Bump version and publish to Artifactory
        if: github.event.inputs.dry_run != 'true'
        run: |
          VERSION_TYPE="${{ steps.version.outputs.version_type }}"
          BRANCH_NAME="${{ steps.version.outputs.branch }}"
          NPM_TAG="${{ steps.version.outputs.npm_tag }}"

          echo "🚀 开始发布流程..."
          echo "📦 分支: $BRANCH_NAME"
          echo "🏷️ 版本类型: $VERSION_TYPE"
          echo "🎯 NPM标签: $NPM_TAG"

          # 检查工作目录状态
          echo "📋 检查 Git 工作目录状态..."
          git status --porcelain

          # 如果有未提交的更改，先提交它们
          if [ -n "$(git status --porcelain)" ]; then
            echo "⚠️ 发现未提交的更改，正在提交..."
            git add .
            git commit -m "chore: prepare for version bump"
          fi

          # 根据版本类型升级版本
          case $VERSION_TYPE in
            "alpha")
              npm version prerelease --preid=alpha --no-git-tag-version
              ;;
            "beta")
              npm version prerelease --preid=beta --no-git-tag-version
              ;;
            "release")
              npm version patch --no-git-tag-version
              ;;
            "patch")
              npm version patch --no-git-tag-version
              ;;
            "minor")
              npm version minor --no-git-tag-version
              ;;
            "major")
              npm version major --no-git-tag-version
              ;;
            *)
              echo "❌ 不支持的版本类型: $VERSION_TYPE"
              exit 1
              ;;
          esac

          # 获取新版本号
          NEW_VERSION=$(node -p "require('./package.json').version")
          echo "NEW_VERSION=$NEW_VERSION" >> $GITHUB_ENV
          echo "✅ 版本已升级到: $NEW_VERSION"

          # 提交版本更改
          git add package.json
          git commit -m "chore: bump version to $NEW_VERSION"

          # 创建并推送标签
          git tag "v$NEW_VERSION"
          git push origin $BRANCH_NAME --tags

          # 最终验证构建产物
          echo "📦 最终验证构建产物..."
          if [ ! -d "dist" ] || [ -z "$(ls -A dist 2>/dev/null)" ]; then
            echo "❌ dist 目录不存在或为空，重新构建..."
            ./scripts/build-lib.sh
          else
            echo "✅ 构建产物验证通过"
            ls -la dist/
          fi

          echo "📋 将要发布的文件:"
          npm pack --dry-run

          # 发布到Artifactory
          echo "🚀 开始发布到 NPM registry..."
          if [ "$NPM_TAG" == "latest" ]; then
            npm publish --verbose
          else
            npm publish --tag $NPM_TAG --verbose
          fi

          echo "🎉 版本 $NEW_VERSION 发布成功！"

      - name: Verify Git tag
        if: github.event.inputs.dry_run != 'true' && (steps.version.outputs.version_type == 'release' || steps.version.outputs.version_type == 'patch' || steps.version.outputs.version_type == 'minor' || steps.version.outputs.version_type == 'major')
        run: |
          echo "🏷️ 验证Git标签: v${{ env.NEW_VERSION }}"
          git tag -l "v${{ env.NEW_VERSION }}"

      - name: Create GitHub Release
        if: github.event.inputs.dry_run != 'true' && (steps.version.outputs.version_type == 'release' || steps.version.outputs.version_type == 'patch' || steps.version.outputs.version_type == 'minor' || steps.version.outputs.version_type == 'major')
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ env.NEW_VERSION }}
          release_name: Release v${{ env.NEW_VERSION }}
          draft: false
          prerelease: false

      - name: Summary
        if: always()
        run: |
          echo "## 📋 发布总结" >> $GITHUB_STEP_SUMMARY
          echo "- **分支**: ${{ steps.version.outputs.branch }}" >> $GITHUB_STEP_SUMMARY
          echo "- **版本类型**: ${{ steps.version.outputs.version_type }}" >> $GITHUB_STEP_SUMMARY
          echo "- **NPM标签**: ${{ steps.version.outputs.npm_tag }}" >> $GITHUB_STEP_SUMMARY
          if [ "${{ github.event.inputs.dry_run }}" == "true" ]; then
            echo "- **模式**: 🧪 干运行（未实际发布）" >> $GITHUB_STEP_SUMMARY
          else
            echo "- **新版本**: ${{ env.NEW_VERSION }}" >> $GITHUB_STEP_SUMMARY
            echo "- **发布状态**: ✅ 成功" >> $GITHUB_STEP_SUMMARY
          fi
