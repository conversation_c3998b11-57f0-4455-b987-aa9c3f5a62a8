name: Publish Only

# 仅用于发布的简化工作流
# 跳过测试，直接构建和发布
on:
  workflow_dispatch:
    inputs:
      version_type:
        description: '版本类型'
        required: true
        type: choice
        default: 'alpha'
        options:
          - alpha
          - beta
          - patch
          - minor
          - major
      skip_build:
        description: '跳过构建（使用现有 dist）'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '18'
  PNPM_VERSION: 'latest'

jobs:
  publish:
    name: Quick Publish
    runs-on: [self-hosted, ali]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Node.js and pnpm
        uses: ./.github/actions/setup-node-pnpm
        with:
          node-version: ${{ env.NODE_VERSION }}
          pnpm-version: ${{ env.PNPM_VERSION }}
          npm-password: ${{ secrets.RT_CDH_NPM_PASSWORD }}
          npm-username: ${{ secrets.RT_CDH_USERNAME }}

      - name: Build library
        if: github.event.inputs.skip_build != 'true'
        run: |
          echo "🔨 构建组件库..."
          pnpm run build:lib

      - name: Verify build artifacts
        run: |
          echo "📦 验证构建产物..."
          if [ ! -d "dist" ] || [ -z "$(ls -A dist 2>/dev/null)" ]; then
            echo "❌ 构建产物不存在，强制重新构建..."
            pnpm run build:lib
          fi
          
          echo "✅ 构建产物验证通过:"
          ls -la dist/

      - name: Configure Git
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"

      - name: Bump version and publish
        run: |
          VERSION_TYPE="${{ github.event.inputs.version_type }}"
          
          echo "🚀 开始发布流程..."
          echo "🏷️ 版本类型: $VERSION_TYPE"

          # 检查工作目录状态
          if [ -n "$(git status --porcelain)" ]; then
            echo "⚠️ 发现未提交的更改，正在提交..."
            git add .
            git commit -m "chore: prepare for version bump"
          fi

          # 升级版本
          case $VERSION_TYPE in
            "alpha")
              npm version prerelease --preid=alpha --no-git-tag-version
              NPM_TAG="alpha"
              ;;
            "beta")
              npm version prerelease --preid=beta --no-git-tag-version
              NPM_TAG="beta"
              ;;
            "patch")
              npm version patch --no-git-tag-version
              NPM_TAG="latest"
              ;;
            "minor")
              npm version minor --no-git-tag-version
              NPM_TAG="latest"
              ;;
            "major")
              npm version major --no-git-tag-version
              NPM_TAG="latest"
              ;;
          esac

          # 获取新版本号
          NEW_VERSION=$(node -p "require('./package.json').version")
          echo "✅ 版本已升级到: $NEW_VERSION"

          # 提交版本更改
          git add package.json
          git commit -m "chore: bump version to $NEW_VERSION"

          # 创建并推送标签
          git tag "v$NEW_VERSION"
          git push origin $(git branch --show-current) --tags

          # 显示发布内容
          echo "📋 将要发布的内容:"
          npm pack --dry-run

          # 发布到 npm
          echo "🚀 发布到 npm registry..."
          if [ "$NPM_TAG" == "latest" ]; then
            npm publish --verbose
          else
            npm publish --tag $NPM_TAG --verbose
          fi

          echo "🎉 版本 $NEW_VERSION 发布成功！"
          echo "📦 安装命令: npm install @odi/web-odi-use@$NPM_TAG"

      - name: Summary
        if: always()
        run: |
          echo "## 📋 发布总结" >> $GITHUB_STEP_SUMMARY
          echo "- **版本类型**: ${{ github.event.inputs.version_type }}" >> $GITHUB_STEP_SUMMARY
          echo "- **跳过构建**: ${{ github.event.inputs.skip_build }}" >> $GITHUB_STEP_SUMMARY
          if [ -f "package.json" ]; then
            VERSION=$(node -p "require('./package.json').version")
            echo "- **发布版本**: $VERSION" >> $GITHUB_STEP_SUMMARY
            echo "- **发布状态**: ✅ 成功" >> $GITHUB_STEP_SUMMARY
          fi
